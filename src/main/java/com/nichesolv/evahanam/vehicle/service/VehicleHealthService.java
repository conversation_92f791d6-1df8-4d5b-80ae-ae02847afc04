package com.nichesolv.evahanam.vehicle.service;

import com.nichesolv.evahanam.vehicle.dto.VehicleHealthResultDto;
import com.nichesolv.evahanam.vehicleModel.enums.PartType;

import java.util.List;
import java.util.Optional;

public interface VehicleHealthService{
    /**
     * Fetches the health status of a vehicle based on its identifier.
     *
     * @param identifier The identification number of the vehicle like IMEI,chassis number,registration no or vehicle id.
     * @return VehicleHealthResultDto containing the health status of the vehicle.
     */
    VehicleHealthResultDto getHealth(String identifier);
    VehicleHealthResultDto getHealthByPartTypes(String identifier, List<PartType> partTypes);
}
