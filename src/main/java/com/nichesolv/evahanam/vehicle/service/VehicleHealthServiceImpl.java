package com.nichesolv.evahanam.vehicle.service;

import com.nichesolv.evahanam.common.exception.PartHealthLimitException;
import com.nichesolv.evahanam.common.util.EvMessageBundle;
import com.nichesolv.evahanam.trip.dto.tripsummary.TripSummaryResponse;
import com.nichesolv.evahanam.trip.repository.TripDetailsRepo;
import com.nichesolv.evahanam.vehicle.dto.AlarmAndAlertCountProjection;
import com.nichesolv.evahanam.vehicle.dto.PartHealthDto;
import com.nichesolv.evahanam.vehicle.dto.PartModelAttributeProjection;
import com.nichesolv.evahanam.vehicle.dto.VehicleHealthResultDto;
import com.nichesolv.evahanam.vehicle.dto.*;
import com.nichesolv.evahanam.vehicle.jpa.*;
import com.nichesolv.evahanam.vehicle.repository.*;
import com.nichesolv.evahanam.vehicleModel.enums.PartType;
import com.nichesolv.evahanam.vehicleModel.exception.PartModelException;
import com.nichesolv.evahanam.vehicleModel.jpa.PartModel;
import com.nichesolv.evahanam.vehicleModel.jpa.VehicleModelPartModelImage;
import com.nichesolv.evahanam.vehicleModel.repository.PartModelAttributeRepository;
import com.nichesolv.evahanam.vehicleModel.repository.VehicleModelPartModelImageRepository;
import com.nichesolv.evahanam.trip.enums.TestRideSummaryPopulationStatus;
import com.nichesolv.evahanam.trip.jpa.Trip;
import com.nichesolv.evahanam.trip.repository.TripRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
public class VehicleHealthServiceImpl implements VehicleHealthService {

    @Autowired
    BatteryHealthMonitorRepository batteryHealthMonitorRepository;

    @Autowired
    MotorHealthMonitorRepository motorHealthMonitorRepository;

    @Autowired
    PartModelAttributeRepository partModelAttributeRepository;

    @Autowired
    VehicleService vehicleService;

    @Autowired
    TripRepository tripRepository;

    @Autowired
    EvMessageBundle evMessageBundle;

    @Autowired
    PartHealthLimitRepository partHealthLimitRepository;

    @Autowired
    TripDetailsRepo tripDetailsRepo;
    @Autowired
    VehicleModelPartModelImageRepository vehicleModelPartModelImageRepository;


    private final String motorInfo = "We rate the vehicle's motor based on the number of alerts and alarms raised by MCS (Motor Controller System) from the date of manufacture.";
    private final String batteryInfo = "We rate the battery based on total number of the alerts raised by the BMS (Battery Management System). This is not reliable if the battery is swapped.";
    private final String tyreInfo = "Using AI we predict the tyre pressure. Optimal tyre pressure is indicated as Normal. When the tyre pressure is not optimal, it is indicated as High or Low based on the calculations.";


    @Override
    public VehicleHealthResultDto getHealth(String identifier) {
        Vehicle vehicle = vehicleService.getVehicleByAnyId(identifier);
        VehicleHealthResultDto vehicleHealthResultDto = new VehicleHealthResultDto();
        List<PartHealthDto> partHealthDtoList = new ArrayList<>();
        List<PartModel> partModels = vehicle.getVehicleParts().stream()
                .map(Part::getPartModel)
                .toList();
        List<VehicleModelPartModelImage> vehicleModelPartModelImages = vehicleModelPartModelImageRepository.findByVehicleModelPartModelImageIdxVehicleModelAndVehicleModelPartModelImageIdxPartModelIn(vehicle.getVehicleModel(), partModels);
        Map<String, ProfileImageDto> profileImages = buildProfileImageMap(vehicleModelPartModelImages);
        Part batteryPart = getPartByType(vehicle.getVehicleParts(), PartType.BATTERY);
        Part motorPart = getPartByType(vehicle.getVehicleParts(), PartType.MOTOR);
        Part mcuPart = getPartByType(vehicle.getVehicleParts(), PartType.MCU);
        Part rearTyrePart = getPartByType(vehicle.getVehicleParts(), PartType.REAR_TYRE);
        if (batteryPart != null) {
            AlarmAndAlertCountProjection batteryCountProjection = batteryHealthMonitorRepository.getAlarmAndAlertCount(batteryPart.getId());
            int batteryAlarmCount = getCount(batteryCountProjection != null ? batteryCountProjection.getAlarmCount() : null);
            int batteryAlertCount = getCount(batteryCountProjection != null ? batteryCountProjection.getAlertCount() : null);
            log.debug("battery alarm count : {}, battery alert count :{} , battery part id{}", batteryAlarmCount, batteryAlertCount, batteryPart.getId());
            getBatteryHealthStatus(batteryPart, batteryAlarmCount, batteryAlertCount, PartType.BATTERY.name(), partHealthDtoList);
        }
        if (motorPart != null && mcuPart != null) {
            AlarmAndAlertCountProjection motorCountProjection = motorHealthMonitorRepository.getAlarmAndAlertCountByPartId(motorPart.getId());
            AlarmAndAlertCountProjection mcuCountProjection = motorHealthMonitorRepository.getAlarmAndAlertCountByPartId(mcuPart.getId());
            int mcuAlarmCount = getCount(mcuCountProjection != null ? mcuCountProjection.getAlarmCount() : null);
            int mcuAlertCount = getCount(mcuCountProjection != null ? mcuCountProjection.getAlertCount() : null);
            int motorAlertCount = getCount(motorCountProjection != null ? motorCountProjection.getAlertCount() : null);
            log.debug("mcu alarm count : {} , mcu alert count : {} , motor alert count : {} , motor part id : {} , final motor count : {}  ", mcuAlarmCount, mcuAlertCount, motorAlertCount, motorPart.getId(), motorAlertCount + mcuAlertCount);
            getMotorHealthStatus(motorPart, mcuAlarmCount, mcuAlertCount + motorAlertCount, PartType.MOTOR.name(), partHealthDtoList);
        }
        if (rearTyrePart != null) {
            log.debug("rear tyre part id {} ", rearTyrePart.getPartModel().getId());
            getRearTyreHealthStatus(vehicle, rearTyrePart, partHealthDtoList);
        }
        vehicleHealthResultDto.setParts(partHealthDtoList);
        vehicleHealthResultDto.setProfileImages(profileImages);
        return vehicleHealthResultDto;

    }


     @Override
     public VehicleHealthResultDto getHealthByPartTypes(String identifier, List<PartType> partTypes) {
        Vehicle vehicle = vehicleService.getVehicleByAnyId(identifier);
        VehicleHealthResultDto vehicleHealthResultDto = new VehicleHealthResultDto();
        List<PartHealthDto> partHealthDtoList = new ArrayList<>();

        for (PartType partType : partTypes) {
            Part part = getPartByType(vehicle.getVehicleParts(), partType);

            if (part == null) {
                continue;
            }

            switch (partType) {
                case BATTERY -> {
                    AlarmAndAlertCountProjection batteryCountProjection =
                            batteryHealthMonitorRepository.getAlarmAndAlertCount(part.getId());

                    int batteryAlarmCount = getCount(batteryCountProjection != null ? batteryCountProjection.getAlarmCount() : null);
                    int batteryAlertCount = getCount(batteryCountProjection != null ? batteryCountProjection.getAlertCount() : null);

                    log.debug("battery alarm count : {}, battery alert count :{} , battery part id{}",
                            batteryAlarmCount, batteryAlertCount, part.getId());

                    getBatteryHealthStatus(part, batteryAlarmCount, batteryAlertCount, PartType.BATTERY.name(), partHealthDtoList);
                }
                case MOTOR -> {
                    Part mcuPart = getPartByType(vehicle.getVehicleParts(), PartType.MCU);
                    if (mcuPart != null) {
                        AlarmAndAlertCountProjection motorCountProjection =
                                motorHealthMonitorRepository.getAlarmAndAlertCountByPartId(part.getId());

                        AlarmAndAlertCountProjection mcuCountProjection =
                                motorHealthMonitorRepository.getAlarmAndAlertCountByPartId(mcuPart.getId());

                        int mcuAlarmCount = getCount(mcuCountProjection != null ? mcuCountProjection.getAlarmCount() : null);
                        int mcuAlertCount = getCount(mcuCountProjection != null ? mcuCountProjection.getAlertCount() : null);
                        int motorAlertCount = getCount(motorCountProjection != null ? motorCountProjection.getAlertCount() : null);

                        log.debug("mcu alarm count : {} , mcu alert count : {} , motor alert count : {} , motor part id : {} , final motor count : {}",
                                mcuAlarmCount, mcuAlertCount, motorAlertCount, part.getId(), motorAlertCount + mcuAlertCount);

                        getMotorHealthStatus(part, mcuAlarmCount, mcuAlertCount + motorAlertCount, PartType.MOTOR.name(), partHealthDtoList);
                    }
                }
                case REAR_TYRE -> {
                    log.debug("rear tyre part id {} ", part.getPartModel().getId());
                    getRearTyreHealthStatus(vehicle, part, partHealthDtoList);
                }
                default -> log.debug("No health check implemented for part type {}", partType);
            }
        }

        vehicleHealthResultDto.setParts(partHealthDtoList);
        return vehicleHealthResultDto;
    }


    private void getBatteryHealthStatus(Part battery, int alarmCount, int alertCount, String partType, List<PartHealthDto> partHealthDtoList) {
        List<PartModelAttributeProjection> partModelAttributes = partModelAttributeRepository.findPartModelAttributes(battery.getPartModel().getId());
        Integer batteryAlarmExcellentValue = 0, batteryAlarmGoodValue = 0, batteryAlarmAverageValue = 0;
        Integer batteryAlertExcellentValue = 0, batteryAlertGoodValue = 0, batteryAlertAverageValue = 0;
        String remark = null;
        log.debug("size {} , part model id {}  ", partModelAttributes.size(), battery.getPartModel().getId());
        for (PartModelAttributeProjection partModelAttributeDto : partModelAttributes) {
            if (partModelAttributeDto.getName().equals("batteryHealthAlarmExcellent")) {
                batteryAlarmExcellentValue = Integer.parseInt(partModelAttributeDto.getValue());
                log.debug(" name : {} , value : {} ", partModelAttributeDto.getName(), partModelAttributeDto.getValue());
            } else if (partModelAttributeDto.getName().equals("batteryHealthAlarmGood")) {
                batteryAlarmGoodValue = Integer.parseInt(partModelAttributeDto.getValue());
                log.debug(" name : {} , value : {} ", partModelAttributeDto.getName(), partModelAttributeDto.getValue());
            } else if (partModelAttributeDto.getName().equals("batteryHealthAlarmAverage")) {
                batteryAlarmAverageValue = Integer.parseInt(partModelAttributeDto.getValue());
                log.debug(" name : {} , value : {} ", partModelAttributeDto.getName(), partModelAttributeDto.getValue());
            } else if (partModelAttributeDto.getName().equals("batteryHealthAlertExcellent")) {
                batteryAlertExcellentValue = Integer.parseInt(partModelAttributeDto.getValue());
                log.debug(" name : {} , value : {} ", partModelAttributeDto.getName(), partModelAttributeDto.getValue());
            } else if (partModelAttributeDto.getName().equals("batteryHealthAlertGood")) {
                batteryAlertGoodValue = Integer.parseInt(partModelAttributeDto.getValue());
                log.debug(" name : {} , value : {} ", partModelAttributeDto.getName(), partModelAttributeDto.getValue());
            } else if (partModelAttributeDto.getName().equals("batteryHealthAlertAverage")) {
                batteryAlertAverageValue = Integer.parseInt(partModelAttributeDto.getValue());
                log.debug(" name : {} , value : {} ", partModelAttributeDto.getName(), partModelAttributeDto.getValue());
            }
        }
        int remarkAlarmValue = calculateRemark(alarmCount, batteryAlarmExcellentValue, batteryAlarmGoodValue, batteryAlarmAverageValue);
        log.debug(" Alarm remark value {} ", remarkAlarmValue);
        int remarkAlertValue = calculateRemark(alertCount, batteryAlertExcellentValue, batteryAlertGoodValue, batteryAlertAverageValue);
        log.debug(" Alert remark value {} ", remarkAlertValue);

        int finalResult = (int) Math.round((double) (remarkAlarmValue + remarkAlertValue) / 2);
        log.debug(" final result : {}", finalResult);
        log.debug("remark : {}", remark);
        PartHealthLimit partHealthLimit = partHealthLimitRepository.findByPartTypeAndValue(battery.getPartType() , finalResult)
                .orElseThrow(() -> new PartHealthLimitException(evMessageBundle.getMessage("PART_HEALTH_LIMIT_NOT_FOUND")));
        PartHealthDto partHealthDto = new PartHealthDto();
        partHealthDto.setPartId(battery.getId());
        partHealthDto.setPartType(partType);
        partHealthDto.setInfo(batteryInfo);
        partHealthDto.setPartLabel(Arrays.asList(partType));
        partHealthDto.setStatus(partHealthLimit.getRemark().name());
        partHealthDtoList.add(partHealthDto);
    }

    private void getMotorHealthStatus(Part motor, int alarmCount, int alertCount, String partType, List<PartHealthDto> partHealthDtoList) {
        List<PartModelAttributeProjection> partModelAttributes = partModelAttributeRepository.findPartModelAttributes(motor.getPartModel().getId());
        Integer motorAlarmExcellentValue = 0, motorAlarmGoodValue = 0, motorAlarmAverageValue = 0;
        Integer motorAlertExcellentValue = 0, motorAlertGoodValue = 0, motorAlertAverageValue = 0;
        String remark = null;
        log.debug("size {} , partModel id {}  ", partModelAttributes.size(), motor.getPartModel().getId());
        for (PartModelAttributeProjection partModelAttributeDto : partModelAttributes) {
            log.debug(" part model attribute {} ", partModelAttributeDto.getName());
            if (partModelAttributeDto.getName().equals("motorHealthAlarmExcellent")) {
                motorAlarmExcellentValue = Integer.parseInt(partModelAttributeDto.getValue());
                log.debug(" name : {} , value : {} ", partModelAttributeDto.getName(), partModelAttributeDto.getValue());
            } else if (partModelAttributeDto.getName().equals("motorHealthAlarmGood")) {
                motorAlarmGoodValue = Integer.parseInt(partModelAttributeDto.getValue());
                log.debug(" name : {} , value : {} ", partModelAttributeDto.getName(), partModelAttributeDto.getValue());
            } else if (partModelAttributeDto.getName().equals("motorHealthAlarmAverage")) {
                motorAlarmAverageValue = Integer.parseInt(partModelAttributeDto.getValue());
                log.debug(" name : {} , value : {} ", partModelAttributeDto.getName(), partModelAttributeDto.getValue());
            } else if (partModelAttributeDto.getName().equals("motorHealthAlertExcellent")) {
                motorAlertExcellentValue = Integer.parseInt(partModelAttributeDto.getValue());
                log.debug(" name : {} , value : {} ", partModelAttributeDto.getName(), partModelAttributeDto.getValue());
            } else if (partModelAttributeDto.getName().equals("motorHealthAlertGood")) {
                motorAlertGoodValue = Integer.parseInt(partModelAttributeDto.getValue());
                log.debug(" name : {} , value : {} ", partModelAttributeDto.getName(), partModelAttributeDto.getValue());
            } else if (partModelAttributeDto.getName().equals("motorHealthAlertAverage")) {
                motorAlertAverageValue = Integer.parseInt(partModelAttributeDto.getValue());
                log.debug(" name : {} , value : {} ", partModelAttributeDto.getName(), partModelAttributeDto.getValue());
            }
        }
        int remarkAlertValue = calculateRemark(alertCount, motorAlertExcellentValue, motorAlertGoodValue, motorAlertAverageValue);
        log.debug(" Alert remark value {} ", remarkAlertValue);
        int remarkAlarmValue = calculateRemark(alarmCount, motorAlarmExcellentValue, motorAlarmGoodValue, motorAlarmAverageValue);
        log.debug(" Alarm remark value {} ", remarkAlarmValue);

        int finalResult = (int) Math.round((double) (remarkAlarmValue + remarkAlertValue) / 2);
        log.debug(" final result : {}", finalResult);
        log.debug("remark : {}", remark);
        PartHealthLimit partHealthLimit = partHealthLimitRepository.findByPartTypeAndValue(motor.getPartType() , finalResult)
                .orElseThrow(() -> new PartHealthLimitException(evMessageBundle.getMessage("PART_HEALTH_LIMIT_NOT_FOUND")));
        PartHealthDto partHealthDto = new PartHealthDto();
        partHealthDto.setPartId(motor.getId());
        partHealthDto.setPartType(partType);
        partHealthDto.setPartLabel(Arrays.asList(partType));
        partHealthDto.setInfo(motorInfo);
        partHealthDto.setStatus(partHealthLimit.getRemark().name());
        partHealthDtoList.add(partHealthDto);
    }

    private int calculateRemark(int count, int excellentValue, int goodValue, int averageValue) {
        int resultValue;
        if (count <= excellentValue) {
            log.debug("Count {} is less than or equal to excellentValue: {}", count, excellentValue);
            resultValue = 0;
        } else if (count <= goodValue) {
            log.debug("Count {} is between excellentValue: {} and goodValue: {}", count, excellentValue, goodValue);
            resultValue = 1;
        } else if (count <= averageValue) {
            log.debug("Count {} is between goodValue: {} and averageValue: {}", count, goodValue, averageValue);
            resultValue = 2;
        } else {
            log.debug("Count {} is greater than averageValue: {}", count, averageValue);
            resultValue = 3;
        }
        return resultValue;
    }

    private void getRearTyreHealthStatus(Vehicle vehicle, Part rearTyre, List<PartHealthDto> partHealthDtoList) {
        List<String> partModelAttributeNameList = Arrays.asList("2wRearTyreLowTyrePressure", "2wRearTyreHighTyrePressure", "2wRearTyreDistanceRatio");
        List<PartModelAttributeProjection> partModelAttributesList = partModelAttributeRepository.getPartAttributesInAList(rearTyre.getPartType().name(), vehicle.getId(), partModelAttributeNameList);
        Float twoWheelRearTyreLowPressure = null, twoWheelRearTyreHighPressure = null;
        Trip trip = tripRepository.findLatestTripForVehicleWhereDurationGreaterThanFiveMinutes(vehicle.getImei(), TestRideSummaryPopulationStatus.COMPLETED.name()).orElse(null);
        if (trip == null) {
            log.warn("No completed trip > 5 minutes found for vehicle: {}", vehicle.getImei());
        }
        if (trip != null) {
            try {
                for (PartModelAttributeProjection partModelAttributeProjection : partModelAttributesList) {
                    switch (partModelAttributeProjection.getName()) {
                        case "2wRearTyreLowTyrePressure":
                            twoWheelRearTyreLowPressure = Float.parseFloat(Optional.ofNullable(partModelAttributeProjection.getValue()).orElseThrow(() -> new PartModelException(evMessageBundle.getMessage("2W_REAR_TYRE_LOW_TYRE_PRESSURE_NOT_FOUND", vehicle.getId()))));
                            log.debug(" tyre health low pressure value {}", twoWheelRearTyreLowPressure);
                            break;
                        case "2wRearTyreHighTyrePressure":
                            twoWheelRearTyreHighPressure = Float.parseFloat(Optional.ofNullable(partModelAttributeProjection.getValue()).orElseThrow(() -> new PartModelException(evMessageBundle.getMessage("2W_REAR_TYRE_HIGH_TYRE_PRESSURE_NOT_FOUND", vehicle.getId()))));
                            log.debug(" tyre health high pressure value {}", twoWheelRearTyreHighPressure);
                            break;
                    }
                }
                Map<String, String> response = tripDetailsRepo.findByTripId(trip.getId()).stream()
                        .collect(
                                Collectors.toMap(TripSummaryResponse::getFieldName,
                                        TripSummaryResponse::getFieldValue
                                ));
                log.info(response.toString());
                Float gpsDistance = Optional.ofNullable(response.get("motorDistance"))
                        .map(Float::parseFloat)
                        .orElseThrow(() -> new IllegalArgumentException("motorDistance is missing or invalid"));

                Float motorDistance = Optional.ofNullable(response.get("gpsDistance"))
                        .map(Float::parseFloat)
                        .orElseThrow(() -> new IllegalArgumentException("gpsDistance is missing or invalid"));

                Float tyrePressureThreshold = Optional.ofNullable(response.get("tyrePressureThreshold"))
                        .map(Float::parseFloat)
                        .orElseThrow(() -> new IllegalArgumentException("tyrePressureThreshold is missing or invalid"));

                int remark = 1;
                log.debug(" gps distance :{} , motor distance :{} ,tyrePressureThreshold :{} ", gpsDistance, motorDistance, tyrePressureThreshold);
                remark = evaluateTyrePressure(tyrePressureThreshold, twoWheelRearTyreLowPressure, twoWheelRearTyreHighPressure, remark);
                PartHealthLimit partHealthLimit = partHealthLimitRepository.findByPartTypeAndValue(rearTyre.getPartType() , remark)
                        .orElseThrow(() -> new PartHealthLimitException(evMessageBundle.getMessage("PART_HEALTH_LIMIT_NOT_FOUND")));
                log.debug(" part health limit {}", partHealthLimit);
                log.debug("remark : {}", remark);
                PartHealthDto partHealthDto = new PartHealthDto();
                partHealthDto.setPartId(rearTyre.getId());
                partHealthDto.setPartType("TYRE");
                partHealthDto.setPartLabel(Arrays.asList("REAR_TYRE", "FRONT_TYRE"));
                partHealthDto.setInfo(tyreInfo);
                partHealthDto.setStatus(partHealthLimit.getRemark().name());
                partHealthDtoList.add(partHealthDto);
            } catch (Exception e) {
                log.error("Error calculating tyre pressure status for vehicle IMEI {}: {}", vehicle.getImei(), e.getMessage());
            }
        }
    }

    private int evaluateTyrePressure(Float tyrePressureThreshold, Float twoWheelRearTyreLowPressure, Float twoWheelRearTyreHighPressure, int remark) {
        if (tyrePressureThreshold < twoWheelRearTyreLowPressure) {
            remark = 0;
        } else if (tyrePressureThreshold <= twoWheelRearTyreLowPressure && tyrePressureThreshold >= twoWheelRearTyreHighPressure) {
            remark = 1;
        } else if (tyrePressureThreshold > twoWheelRearTyreHighPressure) {
            remark = 2;
        }
        return remark;
    }

    private Part getPartByType(Set<Part> parts, PartType type) {
        return parts.stream().filter(part -> part.getPartType() == type).findFirst().orElse(null);
    }

    private int getCount(Integer count) {
        return Optional.ofNullable(count).orElse(0);
    }

    private Map<String, ProfileImageDto> buildProfileImageMap(List<VehicleModelPartModelImage> vehicleModelPartModelImages) {
        return vehicleModelPartModelImages.stream()
                .collect(Collectors.groupingBy(
                        img -> img.getVehicleModelPartModelImageIdx().getImage().getTag(),
                        Collectors.toMap(
                                img -> img.getVehicleModelPartModelImageIdx().getPartModel().getPartType(),
                                VehicleModelPartModelImage::getCoordinates
                        )
                ))
                .entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> {
                            String tag = entry.getKey();
                            String url = vehicleModelPartModelImages.stream()
                                    .filter(img -> img.getVehicleModelPartModelImageIdx().getImage().getTag().equals(tag))
                                    .findFirst()
                                    .map(img -> img.getVehicleModelPartModelImageIdx().getImage().getUrl())
                                    .orElse("");

                            List<PartListCoordinatesDto> partListCoordinates = entry.getValue().entrySet().stream()
                                    .map(partEntry -> {
                                        String[] coordinates = partEntry.getValue().replaceAll("[()]", "").split(",");
                                        return new PartListCoordinatesDto(
                                                partEntry.getKey().name(),
                                                partEntry.getKey().equals(PartType.REAR_TYRE) || partEntry.getKey().equals(PartType.FRONT_TYRE) ? "TYRE" : partEntry.getKey().name(),
                                                Float.parseFloat(coordinates[0].trim()),
                                                Float.parseFloat(coordinates[1].trim())
                                        );
                                    })
                                    .collect(Collectors.toList());

                            return new ProfileImageDto(url, partListCoordinates);
                        }
                ));
    }
}
