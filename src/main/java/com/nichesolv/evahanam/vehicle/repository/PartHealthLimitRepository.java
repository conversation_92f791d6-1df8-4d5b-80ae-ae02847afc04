package com.nichesolv.evahanam.vehicle.repository;

import com.nichesolv.evahanam.vehicle.jpa.PartHealthLimit;
import com.nichesolv.evahanam.vehicleModel.enums.PartType;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface PartHealthLimitRepository extends JpaRepository<PartHealthLimit, Long> {
    Optional<PartHealthLimit> findByPartTypeAndValue(PartType partType, int value);
    List<Optional<PartHealthLimit>> findByPartType(PartType partType);

}
