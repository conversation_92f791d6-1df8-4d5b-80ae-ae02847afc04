package com.nichesolv.evahanam.telemetryData.repository.bmsrepo;

import com.nichesolv.evahanam.telemetryData.jpa.bmsdata.BatteryAlarm;
import com.nichesolv.evahanam.vehicle.dto.AlarmAggregateDto;
import com.nichesolv.evahanam.vehicle.dto.AlarmAggregateProjection;
import com.nichesolv.evahanam.vehicle.dto.AlarmNameCountProjection;
import com.nichesolv.evahanam.vehicle.dto.ImeiPartTypeCountProjection;
import com.nichesolv.evahanam.vehicleModel.enums.PartType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;

@Repository
public interface BatteryAlarmRepository extends AbstractBatteryAlarmRepository<BatteryAlarm> {

    @Query(value = "SELECT imei , part_type as partType , COUNT(*) FROM evdata.battery_alarm WHERE timestamp > ?1 AND timestamp <= ?2 AND part_type IN (?3 , ?4) GROUP BY imei , part_type ", nativeQuery = true)
    List<ImeiPartTypeCountProjection> getImeiPartTypeCountInLastMinute(Instant oneMinutePrior, Instant now, String bmsPartType , String mcuPartType);

    @Query(value = "SELECT distinct ba.vehicle_id FROM evdata.alarm_count_aggregate_1m ba JOIN evdata.fleet_vehicle fv ON fv.vehicle_id = ba.vehicle_id " +
            "JOIN evdata.fleet f ON fv.fleet_id = f.id WHERE f.id = ?1 AND ba.bucket BETWEEN ?2 AND ?3", nativeQuery = true)
    List<Long> getVehicleIdListFromFleet(Long vehicleId, Instant startTime, Instant endTime);

    @Query(value = "SELECT distinct ba.vehicle_id FROM evdata.alarm_count_aggregate_1m ba JOIN evdata.vehicle v ON ba.vehicle_id = v.id WHERE v.mfr_org_id = ?1 " +
            "AND ba.bucket BETWEEN ?2 AND ?3", nativeQuery = true)
    List<Long> getVehicleIdListFromOrganisation(Long orgId, Instant startTime, Instant endTime);

    @Query(value = "SELECT distinct vehicle_id FROM evdata.alarm_count_aggregate_1m WHERE bucket BETWEEN ?1 AND ?2 AND vehicle_id IN ?3", nativeQuery = true)
    List<Long> getVehicleIdFromImeiListBetweenTimestamp(Instant startTime, Instant endTime, List<Long> vehicleIdList);

    @Query(value = """
    SELECT imei, vehicle_id as vehicleId, part_type as partType, alarm_name as alarmName, SUM(alarm_count) as alarmCount FROM evdata.alarm_count_aggregate_1m WHERE bucket BETWEEN ?1 AND ?2 AND part_type = ?3 AND vehicle_id IN (?4) AND alarm_name in (?5) GROUP BY imei, vehicle_id, part_type, alarm_name 
    """, countQuery = """
    SELECT COUNT(vehicle_id) FROM ( SELECT imei, vehicle_id, part_type, alarm_name FROM evdata.alarm_count_aggregate_1m WHERE bucket BETWEEN ?1 AND ?2 AND part_type = ?3 AND vehicle_id IN (?4) AND alarm_name in (?5) GROUP BY imei, vehicle_id, part_type, alarm_name ) AS count_table
    """, nativeQuery = true)
    Page<AlarmAggregateProjection> getAlarmCountsByPartTypeBetweenTimestamp(Instant from, Instant to, String partType, List<Long> vehicleId, List<String> alarms, Pageable pageable);

    @Query(value = """
    SELECT DISTINCT alarm_name FROM evdata.alarm_count_aggregate_1m WHERE part_type = ?1
    """, nativeQuery = true)
    List<String> getDistinctAlarmNamesByPartType(String partType);


    @Query(value = "SELECT COUNT(*) FROM battery_alarm WHERE imei = ?1 AND part_type = ?2", nativeQuery = true)
    Long countByIdImeiAndIdPartType(String imei, String partType);

    @Query(value = """
    SELECT (SELECT aty.name FROM alarm_type aty WHERE aty.id = ba.alarm_type_id) AS name,
           COUNT(*) AS count
    FROM evdata.battery_alarm ba
    WHERE ba.vehicle_id = ?1
      AND ba.part_type = ?2
      AND ba.timestamp BETWEEN ?3 AND ?4
    GROUP BY ba.alarm_type_id
    """, nativeQuery = true)
    List<AlarmNameCountProjection> findBatteryAlarms(
            Long vehicleId,
            String partType,
            Instant startTime,
            Instant endTime
    );


}